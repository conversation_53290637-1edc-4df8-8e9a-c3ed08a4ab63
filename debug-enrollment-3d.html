<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Enrollment-3D API</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .section {
            margin: 20px 0;
            padding: 15px;
            border: 1px solid #ddd;
            border-radius: 5px;
        }
        button {
            background-color: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background-color: #0056b3;
        }
        .log-output {
            background-color: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 5px;
            padding: 10px;
            margin: 10px 0;
            white-space: pre-wrap;
            font-family: monospace;
            max-height: 400px;
            overflow-y: auto;
        }
        .success { color: #28a745; }
        .error { color: #dc3545; }
        .warning { color: #ffc107; }
    </style>
</head>
<body>
    <h1>Debug Enrollment-3D API</h1>
    
    <div class="section">
        <h2>Test API Call</h2>
        <p>This will send a test request to the enrollment-3d endpoint with sample data to help debug missing parameters.</p>
        <button onclick="testEnrollment3D()">Send Test Request</button>
        <button onclick="clearLogs()">Clear Logs</button>
    </div>

    <div class="section">
        <h2>Request/Response Logs</h2>
        <div id="logs" class="log-output">
            Logs will appear here...
        </div>
    </div>

    <script>
        function log(message, type = 'info') {
            const logsDiv = document.getElementById('logs');
            const timestamp = new Date().toISOString();
            const className = type === 'error' ? 'error' : type === 'success' ? 'success' : type === 'warning' ? 'warning' : '';
            logsDiv.innerHTML += `<span class="${className}">[${timestamp}] ${message}</span>\n`;
            logsDiv.scrollTop = logsDiv.scrollHeight;
        }

        function clearLogs() {
            document.getElementById('logs').innerHTML = 'Logs cleared...\n';
        }

        async function testEnrollment3D() {
            log('🚀 Starting enrollment-3d API test...', 'info');
            
            // Sample data that matches the expected structure
            const testData = {
                function: 'liveness',
                faceScan: 'sample_face_scan_data_base64_encoded_string_would_be_here',
                auditTrailImage: 'sample_audit_trail_image_base64_encoded_string_would_be_here',
                lowQualityAuditTrailImage: 'sample_low_quality_audit_trail_image_base64_encoded_string_would_be_here',
                sessionId: 'test-session-id-12345'
            };

            const testHeaders = {
                'Content-Type': 'application/json',
                'Authorization': 'Bearer test-token',
                'X-Device-Key': 'test-device-key',
                'X-User-Agent': 'test-user-agent',
                'X-Session-Id': 'test-session-id',
                'X-Tid': 'test-tid',
                'X-Ekyc-Token': 'test-ekyc-token',
                'X-Ekyc-Sdk-Version': '1.0.0',
                'correlationid': 'test-correlation-id'
            };

            log('📤 Sending request with data:', 'info');
            log(JSON.stringify(testData, null, 2), 'info');
            log('📤 Sending request with headers:', 'info');
            log(JSON.stringify(testHeaders, null, 2), 'info');

            try {
                const response = await fetch('/api/enrollment-3d', {
                    method: 'POST',
                    headers: testHeaders,
                    body: JSON.stringify(testData)
                });

                log(`📥 Response status: ${response.status}`, response.ok ? 'success' : 'error');
                
                const responseData = await response.json();
                log('📥 Response data:', 'info');
                log(JSON.stringify(responseData, null, 2), response.ok ? 'success' : 'error');

                if (response.ok) {
                    log('✅ Test completed successfully!', 'success');
                } else {
                    log('❌ Test failed with error response', 'error');
                }

            } catch (error) {
                log(`❌ Network error: ${error.message}`, 'error');
                log(`Error details: ${error.stack}`, 'error');
            }

            log('🏁 Test completed. Check browser console and server logs for detailed information.', 'info');
        }

        // Log initial message
        log('Debug page loaded. Click "Send Test Request" to test the enrollment-3d API.', 'info');
    </script>
</body>
</html>
