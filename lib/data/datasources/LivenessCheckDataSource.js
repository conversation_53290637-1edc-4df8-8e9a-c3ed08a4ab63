const DeveloperStatusMessages = require('../../infrastructure/utils/DeveloperStatusMessages');

/**
 * Data source for Liveness Check API operations
 * Handles direct API communication for liveness check functionality
 */
class LivenessCheckDataSource {
    constructor() {
        this.baseUrl = '/api'; // Use Next.js API routes as proxy
    }

    /**
     * Post liveness check data to the backend API
     * @param {Object} livenessData - The liveness data including face scan and audit trail
     * @param {Object} headers - Additional headers for the request
     * @param {Function} onProgress - Progress callback function
     * @returns {Promise<Object>} - API response
     */
    async postLivenessCheck(livenessData, headers = {}, onProgress = null) {
        return new Promise((resolve, reject) => {
            const startTime = performance.now();
            
            try {
                const url = `${this.baseUrl}/enrollment-3d`;

                DeveloperStatusMessages.logApiCall(url, 'POST', 'Starting request');
                
                // Create XMLHttpRequest for progress tracking
                const xhr = new XMLHttpRequest();
                
                // Set up progress tracking
                if (onProgress && typeof onProgress === 'function') {
                    xhr.upload.onprogress = function(event) {
                        if (event.lengthComputable) {
                            const progress = event.loaded / event.total;
                            DeveloperStatusMessages.logLivenessProgress('Uploading', progress);
                            onProgress(progress);
                        }
                    };
                }

                // Set up response handlers
                xhr.onreadystatechange = function() {
                    if (xhr.readyState === XMLHttpRequest.DONE) {
                        const endTime = performance.now();
                        
                        try {
                            if (xhr.status >= 200 && xhr.status < 300) {
                                const responseData = JSON.parse(xhr.responseText);
                                DeveloperStatusMessages.logPerformance('LivenessCheckDataSource.postLivenessCheck', startTime, endTime);
                                DeveloperStatusMessages.logApiCall(url, 'POST', `Success (${xhr.status})`);
                                DeveloperStatusMessages.logData('API Response', {
                                    status: xhr.status,
                                    wasProcessed: responseData.wasProcessed,
                                    error: responseData.error,
                                    hasScanResultBlob: !!responseData.scanResultBlob
                                });
                                resolve(responseData);
                            } else {
                                DeveloperStatusMessages.logPerformance('LivenessCheckDataSource.postLivenessCheck (failed)', startTime, endTime);
                                DeveloperStatusMessages.logError(`API call failed with status ${xhr.status}`);
                                reject(new Error(`HTTP error! status: ${xhr.status}`));
                            }
                        } catch (error) {
                            DeveloperStatusMessages.logError('Error parsing response', error);
                            reject(error);
                        }
                    }
                };
                
                // Set up error handler
                xhr.onerror = function() {
                    DeveloperStatusMessages.logError('Network error occurred');
                    reject(new Error('Network error occurred'));
                };
                
                // Open and send the request
                xhr.open('POST', url, true);
                
                // Set headers
                xhr.setRequestHeader('Content-Type', 'application/json');
                for (const [key, value] of Object.entries(headers)) {
                    if (value !== undefined && value !== null) {
                        xhr.setRequestHeader(key, value);
                    }
                }
                
                // Send the request
                const requestBody = JSON.stringify(livenessData);
                DeveloperStatusMessages.logData('Request Body Keys', Object.keys(livenessData));
                xhr.send(requestBody);
            } catch (error) {
                DeveloperStatusMessages.logError('Error in postLivenessCheck', error);
                reject(error);
            }
        });
    }
}

module.exports = LivenessCheckDataSource;
