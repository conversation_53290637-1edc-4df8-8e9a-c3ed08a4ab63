const FaceTecRepository = require('../../data/repositories/FaceTecRepository');

/**
 * UseCase for handling Liveness Check API calls
 */
class PostLivenessCheckUseCase {
  constructor() {
    this.faceTecRepository = new FaceTecRepository();
  }

  /**
   * Execute the Liveness Check
   * @param {Object} params - Parameters for the liveness check
   * @param {Object} params.sessionResult - The FaceTec session result
   * @param {string} params.deviceKey - Optional device key for API
   * @param {Object} params.additionalHeaders - Additional headers to include in the request
   * @param {Function} params.onProgress - Callback for upload progress
   * @returns {Promise<Object>} - Result of the liveness check
   */
  async execute(params) {
    try {
      const { sessionResult, deviceKey, additionalHeaders, onProgress } = params;

      console.log('=== POST LIVENESS CHECK USE CASE - STARTING ===');
      console.log('Input params:', {
        hasSessionResult: !!sessionResult,
        deviceKey: deviceKey,
        additionalHeaders: additionalHeaders,
        hasOnProgress: typeof onProgress === 'function'
      });

      // Log session result details
      if (sessionResult) {
        console.log('Session result details:', {
          sessionId: sessionResult.sessionId,
          hasFaceScan: !!sessionResult.faceScan,
          hasAuditTrail: !!sessionResult.auditTrail && sessionResult.auditTrail.length > 0,
          hasLowQualityAuditTrail: !!sessionResult.lowQualityAuditTrail && sessionResult.lowQualityAuditTrail.length > 0,
          auditTrailLength: sessionResult.auditTrail ? sessionResult.auditTrail.length : 0,
          lowQualityAuditTrailLength: sessionResult.lowQualityAuditTrail ? sessionResult.lowQualityAuditTrail.length : 0
        });
      }

      // Prepare headers
      const headers = { ...additionalHeaders };
      if (deviceKey) {
        headers['X-Device-Key'] = deviceKey;
      }

      // Add FaceTec user agent header
      if (sessionResult.sessionId && typeof FaceTecSDK !== 'undefined') {
        headers['X-User-Agent'] = FaceTecSDK.createFaceTecAPIUserAgentString(sessionResult.sessionId);
      }

      console.log('Prepared headers:', JSON.stringify(headers, null, 2));

      // Prepare parameters for the API call
      const livenessData = {
        faceScan: sessionResult.faceScan,
        auditTrailImage: sessionResult.auditTrail[0],
        lowQualityAuditTrailImage: sessionResult.lowQualityAuditTrail[0],
        sessionId: sessionResult.sessionId,
        function: 'liveness'
      };

      console.log('=== POST LIVENESS CHECK USE CASE - PREPARED DATA ===');
      console.log('Liveness data keys:', Object.keys(livenessData));
      console.log('Liveness data structure:', {
        function: livenessData.function,
        sessionId: livenessData.sessionId,
        hasFaceScan: !!livenessData.faceScan,
        hasAuditTrailImage: !!livenessData.auditTrailImage,
        hasLowQualityAuditTrailImage: !!livenessData.lowQualityAuditTrailImage,
        faceScanLength: livenessData.faceScan ? livenessData.faceScan.length : 0,
        auditTrailImageLength: livenessData.auditTrailImage ? livenessData.auditTrailImage.length : 0,
        lowQualityAuditTrailImageLength: livenessData.lowQualityAuditTrailImage ? livenessData.lowQualityAuditTrailImage.length : 0
      });

      // Call the repository
      console.log('Calling repository.submitLivenessCheck...');
      const response = await this.faceTecRepository.submitLivenessCheck(livenessData, headers, onProgress);

      console.log('=== POST LIVENESS CHECK USE CASE - REPOSITORY RESPONSE ===');
      console.log('Repository response:', JSON.stringify(response, null, 2));

      // Process the response
      if (response.wasProcessed === true && response.error === false) {
        const successResult = {
          success: true,
          scanResultBlob: response.scanResultBlob
        };
        console.log('✅ USE CASE SUCCESS - Returning:', successResult);
        return successResult;
      } else {
        const errorResult = {
          success: false,
          errorMessage: response.errorMessage || "Server returned an error."
        };
        console.log('❌ USE CASE ERROR - Returning:', errorResult);
        return errorResult;
      }
    } catch (error) {
      console.error("❌ PostLivenessCheckUseCase error:", error);
      throw error;
    }
  }
}

module.exports = PostLivenessCheckUseCase;