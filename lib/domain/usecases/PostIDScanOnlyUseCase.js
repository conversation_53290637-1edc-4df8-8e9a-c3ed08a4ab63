const FaceTecRepository = require('../../data/repositories/FaceTecRepository');
const UuidGenerator = require('../../infrastructure/utils/UuidGenerator');
const DeveloperStatusMessages = require('../../infrastructure/utils/DeveloperStatusMessages');

/**
 * Use case for posting ID scan data
 * Contains business logic for ID scanning operations
 */
class PostIDScanOnlyUseCase {
    constructor() {
        this.faceTecRepository = new FaceTecRepository();
    }

    /**
     * Execute the ID scan submission process
     * @param {Object} params - Parameters for the use case
     * @param {Object} params.idScanResult - FaceTec ID scan result
     * @param {string} params.deviceKey - Device key for authentication
     * @param {Object} params.additionalHeaders - Additional headers for the request
     * @param {Function} params.onProgress - Progress callback function
     * @returns {Promise<Object>} - Use case result
     */
    async execute({ idScanResult, deviceKey, additionalHeaders = {}, onProgress = null }) {
        const startTime = performance.now();
        
        try {
            DeveloperStatusMessages.logMessage('Starting PostIDScanOnlyUseCase execution');
            
            // Prepare scan data from FaceTec result
            DeveloperStatusMessages.logMessage('Preparing scan data...');
            const scanData = this.prepareScanData(idScanResult);
            DeveloperStatusMessages.logData('Scan Data Keys', Object.keys(scanData));
            
            // Prepare headers
            DeveloperStatusMessages.logMessage('Preparing headers...');
            const headers = this.prepareHeaders(idScanResult, deviceKey, additionalHeaders);
            DeveloperStatusMessages.logData('Request Headers', Object.keys(headers));
            
            // Validate scan data
            DeveloperStatusMessages.logMessage('Validating scan data...');
            this.faceTecRepository.validateScanData(scanData);
            DeveloperStatusMessages.logSuccess('Scan data validation passed');
            
            // Submit to repository with progress tracking
            DeveloperStatusMessages.logMessage('Submitting to repository...');
            const response = await this.faceTecRepository.submitIDScan(scanData, headers, onProgress);
            
            // Process the response according to business rules
            DeveloperStatusMessages.logMessage('Processing response...');
            const result = this.processResponse(response);
            
            const endTime = performance.now();
            DeveloperStatusMessages.logPerformance('PostIDScanOnlyUseCase.execute', startTime, endTime);
            DeveloperStatusMessages.logSuccess(`UseCase completed successfully: ${result.success}`);
            
            return result;
            
        } catch (error) {
            const endTime = performance.now();
            DeveloperStatusMessages.logPerformance('PostIDScanOnlyUseCase.execute (failed)', startTime, endTime);
            DeveloperStatusMessages.logError('PostIDScanOnlyUseCase - execute error', error);
            throw error;
        }
    }

    /**
     * Prepare scan data from FaceTec ID scan result
     * @param {Object} idScanResult - FaceTec ID scan result
     * @returns {Object} - Prepared scan data
     */
    prepareScanData(idScanResult) {
        const parameters = {
            idScan: idScanResult.idScan,
            enableConfirmInfo: true // default value follow spec
        };

        // Add front image if available
        if (idScanResult.frontImages && idScanResult.frontImages[0]) {
            parameters.idScanFrontImage = idScanResult.frontImages[0];
        }

        // Add back image if available
        if (idScanResult.backImages && idScanResult.backImages[0]) {
            parameters.idScanBackImage = idScanResult.backImages[0];
        }

        return parameters;
    }

    /**
     * Prepare headers for the API request
     * @param {Object} idScanResult - FaceTec ID scan result
     * @param {string} deviceKey - Device key
     * @param {Object} additionalHeaders - Additional headers
     * @returns {Object} - Prepared headers
     */
    prepareHeaders(idScanResult, deviceKey, additionalHeaders) {
        const headers = {};

        // Add device key if available
        if (deviceKey) {
            headers['X-Device-Key'] = deviceKey;
        }

        // Add FaceTec user agent
        if (idScanResult.sessionId) {
            headers['X-User-Agent'] = FaceTecSDK.createFaceTecAPIUserAgentString(idScanResult.sessionId);
        }

        // Add additional headers
        if (additionalHeaders.Authorization) {
            headers['Authorization'] = additionalHeaders.Authorization;
        }
        if (additionalHeaders['X-Session-Id']) {
            headers['X-Session-Id'] = additionalHeaders['X-Session-Id'];
        }
        if (additionalHeaders['X-Ekyc-Token']) {
            headers['X-Ekyc-Token'] = additionalHeaders['X-Ekyc-Token'];
        }
        if (additionalHeaders.correlationid) {
            headers['correlationid'] = additionalHeaders.correlationid;
        }

        // Generate X-Tid if not provided (mandatory field)
        // Generate UUID v4 for transaction ID using UuidGenerator
        headers['X-Tid'] = UuidGenerator.getUniqueId();

        return headers;
    }

    /**
     * Process the API response according to business rules
     * @param {Object} response - API response
     * @returns {Object} - Processed response
     */
    processResponse(response) {
        // Business logic for processing response
        // This is where you can add any business rules for handling the response

        // Extract OCR data from the original response if available
        let userOcrValue = null;
        if (response.originalResponse && response.originalResponse.data && response.originalResponse.data.userOcrValue) {
            userOcrValue = response.originalResponse.data.userOcrValue;
            DeveloperStatusMessages.logData('Extracted OCR Data', Object.keys(userOcrValue));
        } else {
            DeveloperStatusMessages.logMessage('No OCR data found in response');
        }

        return {
            success: response.wasProcessed === true && response.error === false,
            scanResultBlob: response.scanResultBlob,
            originalResponse: response.originalResponse,
            errorMessage: response.errorMessage,
            userOcrValue: userOcrValue
        };
    }
}

module.exports = PostIDScanOnlyUseCase; 