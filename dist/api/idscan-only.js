!function(e,s){"object"==typeof exports&&"object"==typeof module?module.exports=s():"function"==typeof define&&define.amd?define("api/idscan-only",[],s):"object"==typeof exports?exports["api/idscan-only"]=s():e["api/idscan-only"]=s()}(this,(()=>{return e={235:e=>{async function s(e,s){if("POST"!==e.method)return s.status(405).json({error:"Method not allowed"});try{const o=`${process.env.API_BASE_URL||"https://ekyc-ekyc-dev.np.scbtechx.io"}/v1/ekyc/idscan-only`,r={"Content-Type":"application/json",Accept:"application/json",Authorization:e.headers.authorization||(process.env.JWT_TOKEN?`Bearer ${process.env.JWT_TOKEN}`:void 0),"X-Device-Key":e.headers["x-device-key"],"X-User-Agent":e.headers["x-user-agent"],"X-Session-Id":e.headers["x-session-id"],"X-Tid":e.headers["x-tid"],"X-Ekyc-Token":e.headers["x-ekyc-token"],correlationid:e.headers.correlationid};Object.keys(r).forEach((e=>{void 0===r[e]&&delete r[e]}));const t=await fetch(o,{method:"POST",headers:r,body:JSON.stringify(e.body)}),n=await t.json();if(t.ok&&"CUS-KYC-1000"===n.code){const e={wasProcessed:!0,error:!1,scanResultBlob:n.data?.scanResultBlob||"",originalResponse:n};return console.log("Returning success response with scanResultBlob length:",e.scanResultBlob.length),s.status(200).json(e)}{const e={wasProcessed:!1,error:!0,errorMessage:n.description||n.message||"Unknown error",originalResponse:n};return console.log("Returning error response:",e.errorMessage),s.status(t.status).json(e)}}catch(e){return console.error("Error processing ID scan:",e),s.status(500).json({wasProcessed:!1,error:!0,errorMessage:"Failed to process ID scan"})}}e.exports=s,e.exports.default=s}},s={},function o(r){var t=s[r];if(void 0!==t)return t.exports;var n=s[r]={exports:{}};return e[r](n,n.exports,o),n.exports}(235);var e,s}));