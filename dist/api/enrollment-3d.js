(function webpackUniversalModuleDefinition(root, factory) {
	if(typeof exports === 'object' && typeof module === 'object')
		module.exports = factory();
	else if(typeof define === 'function' && define.amd)
		define("ScbTechXEkycSDK", [], factory);
	else if(typeof exports === 'object')
		exports["ScbTechXEkycSDK"] = factory();
	else
		root["ScbTechXEkycSDK"] = root["ScbTechXEkycSDK"] || {}, root["ScbTechXEkycSDK"]["api/enrollment-3d"] = factory();
})(this, () => {
return /******/ (() => { // webpackBootstrap
/******/ 	var __webpack_modules__ = ({

/***/ 622:
/***/ ((module) => {

/**
 * API route to process liveness check data to the eKYC backend API
 * This acts as a proxy to avoid CORS issues when calling from the browser
 *
 * @param {import('next').NextApiRequest} req
 * @param {import('next').NextApiResponse} res
 */
async function handler(req, res) {
  // Only allow POST requests
  if (req.method !== 'POST') {
    return res.status(405).json({ error: 'Method not allowed' });
  }

  try {
    // Helper function to truncate values for logging
    const truncateForLogging = (obj, maxLength = 20) => {
      if (!obj || typeof obj !== 'object') return obj;

      const truncated = {};
      for (const [key, value] of Object.entries(obj)) {
        if (typeof value === 'string' && value.length > maxLength) {
          truncated[key] = value.substring(0, maxLength) + '...';
        } else if (typeof value === 'object' && value !== null) {
          truncated[key] = truncateForLogging(value, maxLength);
        } else {
          truncated[key] = value;
        }
      }
      return truncated;
    };

    // Log incoming request details
    console.log('=== ENROLLMENT-3D API HANDLER - INCOMING REQUEST ===');
    console.log('Request method:', req.method);
    console.log('Request headers received (truncated):', JSON.stringify(truncateForLogging(req.headers), null, 2));
    console.log('Request body received (truncated):', JSON.stringify(truncateForLogging(req.body), null, 2));
    console.log('Request body keys:', Object.keys(req.body || {}));

    // Check for required body parameters
    const requiredBodyParams = ['function', 'faceScan', 'auditTrailImage', 'lowQualityAuditTrailImage'];
    const missingParams = requiredBodyParams.filter(param => !req.body || !req.body[param]);
    if (missingParams.length > 0) {
      console.log('⚠️  MISSING REQUIRED BODY PARAMETERS:', missingParams);
    } else {
      console.log('✅ All required body parameters present');
    }

    // Debug X-Tid header specifically
    console.log('=== X-TID HEADER DEBUG ===');
    console.log('req.headers[\'x-tid\']:', req.headers['x-tid']);
    console.log('req.headers[\'X-Tid\']:', req.headers['X-Tid']);
    console.log('req.headers[\'X-TID\']:', req.headers['X-TID']);
    console.log('req.headers[\'x-TID\']:', req.headers['x-TID']);

    // Check all header keys that contain 'tid'
    const tidHeaders = Object.keys(req.headers).filter(key => key.toLowerCase().includes('tid'));
    console.log('Headers containing "tid":', tidHeaders);
    tidHeaders.forEach(key => {
      console.log(`  ${key}: ${req.headers[key]}`);
    });

    // Set the base URL - this should be configured based on environment
    const baseUrl = process.env.API_BASE_URL || 'https://ekyc-ekyc-dev.np.scbtechx.io';
    const url = `${baseUrl}/v1/ekyc/enrollment-3d`;

    // Debug header values before creating headers object
    console.log('=== HEADER VALUES BEFORE MAPPING ===');
    console.log('authorization:', req.headers['authorization']);
    console.log('x-device-key:', req.headers['x-device-key']);
    console.log('x-user-agent:', req.headers['x-user-agent']);
    console.log('x-session-id:', req.headers['x-session-id']);
    console.log('x-tid:', req.headers['x-tid']);
    console.log('x-ekyc-token:', req.headers['x-ekyc-token']);
    console.log('correlationid:', req.headers.correlationid);

    // Forward headers follow spec
    const headers = {
      'Content-Type': 'application/json',
      'Accept': 'application/json',
      'Authorization': req.headers['authorization'] || (process.env.JWT_TOKEN ? `Bearer ${process.env.JWT_TOKEN}` : undefined),
      'X-Device-Key': req.headers['x-device-key'],
      'X-User-Agent': req.headers['x-user-agent'],
      'X-Session-Id': req.headers['x-session-id'],
      'X-Tid': req.headers['x-tid'],
      'X-Ekyc-Token': req.headers['x-ekyc-token'],
      'X-Ekyc-Sdk-Version': '1.0.0',
      'correlationid': req.headers.correlationid
    };

    // Debug headers before cleanup
    console.log('=== HEADERS BEFORE UNDEFINED CLEANUP ===');
    Object.keys(headers).forEach(key => {
      console.log(`${key}: ${headers[key]} (type: ${typeof headers[key]})`);
    });

    // Remove undefined headers
    Object.keys(headers).forEach(key => {
      if (headers[key] === undefined) {
        console.log(`🗑️  REMOVING UNDEFINED HEADER: ${key}`);
        delete headers[key];
      }
    });

    // Debug headers after cleanup
    console.log('=== HEADERS AFTER UNDEFINED CLEANUP ===');
    Object.keys(headers).forEach(key => {
      console.log(`${key}: ${headers[key]}`);
    });

    // Log outgoing request details
    console.log('=== ENROLLMENT-3D API HANDLER - OUTGOING REQUEST ===');
    console.log('Target URL:', url);
    console.log('Outgoing headers (truncated):', JSON.stringify(truncateForLogging(headers), null, 2));
    console.log('Outgoing body (truncated):', JSON.stringify(truncateForLogging(req.body), null, 2));
    console.log('Outgoing body size (bytes):', JSON.stringify(req.body).length);

    // Make the POST request with the body from the client
    const response = await fetch(url, {
      method: 'POST',
      headers: headers,
      body: JSON.stringify(req.body),
    });

    // Get the response data
    const data = await response.json();

    // Log response details
    console.log('=== ENROLLMENT-3D API HANDLER - BACKEND RESPONSE ===');
    console.log('Response status:', response.status);
    console.log('Response ok:', response.ok);
    console.log('Response data:', JSON.stringify(data, null, 2));

    // Transform the response to match LivenessCheckProcessor expectations
    if (response.ok && data.code === 'CUS-KYC-1000') {
      // Success case - return format expected by LivenessCheckProcessor
      const successResponse = {
        wasProcessed: true,
        error: false,
        scanResultBlob: data.data?.scanResultBlob || "", // Use scanResultBlob from backend if available
        originalResponse: data // Keep original response for debugging
      };

      console.log('✅ SUCCESS - Returning success response with scanResultBlob length:', successResponse.scanResultBlob.length);
      console.log('=== ENROLLMENT-3D API HANDLER - FINAL RESPONSE ===');
      console.log('Final response:', JSON.stringify(successResponse, null, 2));
      return res.status(200).json(successResponse);
    } else {
      // Error case - return format expected by LivenessCheckProcessor
      const errorResponse = {
        wasProcessed: false,
        error: true,
        errorMessage: data.description || data.message || 'Unknown error',
        originalResponse: data // Keep original response for debugging
      };

      console.log('❌ ERROR - Returning error response:', errorResponse.errorMessage);
      console.log('=== ENROLLMENT-3D API HANDLER - FINAL RESPONSE ===');
      console.log('Final response:', JSON.stringify(errorResponse, null, 2));
      return res.status(response.status).json(errorResponse);
    }
  } catch (error) {
    console.error('Error processing liveness check:', error);
    return res.status(500).json({
      wasProcessed: false,
      error: true,
      errorMessage: 'Failed to process liveness check'
    });
  }
}

// Export for both CommonJS and ES modules
module.exports = handler;
module.exports["default"] = handler;

/***/ })

/******/ 	});
/************************************************************************/
/******/ 	// The module cache
/******/ 	var __webpack_module_cache__ = {};
/******/ 	
/******/ 	// The require function
/******/ 	function __webpack_require__(moduleId) {
/******/ 		// Check if module is in cache
/******/ 		var cachedModule = __webpack_module_cache__[moduleId];
/******/ 		if (cachedModule !== undefined) {
/******/ 			return cachedModule.exports;
/******/ 		}
/******/ 		// Create a new module (and put it into the cache)
/******/ 		var module = __webpack_module_cache__[moduleId] = {
/******/ 			// no module.id needed
/******/ 			// no module.loaded needed
/******/ 			exports: {}
/******/ 		};
/******/ 	
/******/ 		// Execute the module function
/******/ 		__webpack_modules__[moduleId](module, module.exports, __webpack_require__);
/******/ 	
/******/ 		// Return the exports of the module
/******/ 		return module.exports;
/******/ 	}
/******/ 	
/************************************************************************/
/******/ 	
/******/ 	// startup
/******/ 	// Load entry module and return exports
/******/ 	// This entry module is referenced by other modules so it can't be inlined
/******/ 	var __webpack_exports__ = __webpack_require__(622);
/******/ 	
/******/ 	return __webpack_exports__;
/******/ })()
;
});
//# sourceMappingURL=enrollment-3d.js.map