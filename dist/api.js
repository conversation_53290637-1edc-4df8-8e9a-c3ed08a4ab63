!function(e,s){"object"==typeof exports&&"object"==typeof module?module.exports=s():"function"==typeof define&&define.amd?define("api",[],s):"object"==typeof exports?exports.api=s():e.api=s()}(this,(()=>{return e={22:e=>{function s(e,s){s.status(200).json({status:"UP"})}e.exports=s,e.exports.default=s},154:(e,s,o)=>{const t=o(198),r=o(264),n=o(235),c=o(622),a=o(22);e.exports={sessionTokenHandler:t,facetecSessionTokenHandler:r,idscanOnlyHandler:n,livenessCheckHandler:c,healthHandler:a}},198:(e,s,o)=>{const t=o(599);async function r(e,s){if("GET"!==e.method)return s.status(405).json({error:"Method not allowed"});try{const o=`${process.env.API_BASE_URL||"https://ekyc-ekyc-dev.np.scbtechx.io"}/v1/ekyc/authen/sessiontoken`,r={...e.headers};if(delete r.host,delete r.connection,r["Content-Type"]="application/json",r.Accept="application/json",!r.Authorization&&process.env.JWT_TOKEN&&(r.Authorization=`Bearer ${process.env.JWT_TOKEN}`),!r["X-Ekyc-Device-Info"]){const e=t.getUniqueId();r["X-Ekyc-Device-Info"]=`browser|${e}`}const n=await fetch(o,{method:"GET",headers:r}),c=await n.json();return s.status(n.status).json(c)}catch(e){return console.error("Error getting session token:",e),s.status(500).json({error:"Failed to get session token"})}}e.exports=r,e.exports.default=r},235:e=>{async function s(e,s){if("POST"!==e.method)return s.status(405).json({error:"Method not allowed"});try{const o=`${process.env.API_BASE_URL||"https://ekyc-ekyc-dev.np.scbtechx.io"}/v1/ekyc/idscan-only`,t={"Content-Type":"application/json",Accept:"application/json",Authorization:e.headers.authorization||(process.env.JWT_TOKEN?`Bearer ${process.env.JWT_TOKEN}`:void 0),"X-Device-Key":e.headers["x-device-key"],"X-User-Agent":e.headers["x-user-agent"],"X-Session-Id":e.headers["x-session-id"],"X-Tid":e.headers["x-tid"],"X-Ekyc-Token":e.headers["x-ekyc-token"],correlationid:e.headers.correlationid};Object.keys(t).forEach((e=>{void 0===t[e]&&delete t[e]}));const r=await fetch(o,{method:"POST",headers:t,body:JSON.stringify(e.body)}),n=await r.json();if(r.ok&&"CUS-KYC-1000"===n.code){const e={wasProcessed:!0,error:!1,scanResultBlob:n.data?.scanResultBlob||"",originalResponse:n};return console.log("Returning success response with scanResultBlob length:",e.scanResultBlob.length),s.status(200).json(e)}{const e={wasProcessed:!1,error:!0,errorMessage:n.description||n.message||"Unknown error",originalResponse:n};return console.log("Returning error response:",e.errorMessage),s.status(r.status).json(e)}}catch(e){return console.error("Error processing ID scan:",e),s.status(500).json({wasProcessed:!1,error:!0,errorMessage:"Failed to process ID scan"})}}e.exports=s,e.exports.default=s},264:(e,s,o)=>{const t=o(599);async function r(e,s){if("GET"!==e.method)return s.status(405).json({error:"Method not allowed"});try{const o=`${process.env.API_BASE_URL||"https://ekyc-ekyc-dev.np.scbtechx.io"}/v1/ekyc/authen/sessiontoken/facetec`,r={...e.headers};if(delete r.host,delete r.connection,r["Content-Type"]="application/json",r.Accept="application/json",!r.Authorization&&process.env.JWT_TOKEN&&(r.Authorization=`Bearer ${process.env.JWT_TOKEN}`),!r["X-Ekyc-Device-Info"]){const e=t.getUniqueId();r["X-Ekyc-Device-Info"]=`browser|${e}`}const n=await fetch(o,{method:"GET",headers:r}),c=await n.json();return s.status(n.status).json(c)}catch(e){return console.error("Error getting FaceTec session token:",e),s.status(500).json({error:"Failed to get FaceTec session token"})}}e.exports=r,e.exports.default=r},599:e=>{e.exports=class{static generateUuid(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){const s=16*Math.random()|0;return("x"===e?s:3&s|8).toString(16)}))}static getDeviceId(){if("undefined"!=typeof window&&window.localStorage){let e=localStorage.getItem("ekyc_device_id");return e||(e=this.generateUuid(),localStorage.setItem("ekyc_device_id",e)),e}return this.generateUuid()}static getUniqueId(){return this.generateUuid()}}},622:e=>{async function s(e,s){if("POST"!==e.method)return s.status(405).json({error:"Method not allowed"});try{const o=`${process.env.API_BASE_URL||"https://ekyc-ekyc-dev.np.scbtechx.io"}/v1/ekyc/enrollment-3d`,t={"Content-Type":"application/json",Accept:"application/json",Authorization:e.headers.authorization||(process.env.JWT_TOKEN?`Bearer ${process.env.JWT_TOKEN}`:void 0),"X-Device-Key":e.headers["x-device-key"],"X-User-Agent":e.headers["x-user-agent"],"X-Session-Id":e.headers["x-session-id"],"X-Tid":e.headers["x-tid"],"X-Ekyc-Token":e.headers["x-ekyc-token"],correlationid:e.headers.correlationid};Object.keys(t).forEach((e=>{void 0===t[e]&&delete t[e]}));const r=await fetch(o,{method:"POST",headers:t,body:JSON.stringify(e.body)}),n=await r.json();if(r.ok&&"CUS-KYC-1000"===n.code){const e={wasProcessed:!0,error:!1,scanResultBlob:n.data?.scanResultBlob||"",originalResponse:n};return console.log("Returning success response with scanResultBlob length:",e.scanResultBlob.length),s.status(200).json(e)}{const e={wasProcessed:!1,error:!0,errorMessage:n.description||n.message||"Unknown error",originalResponse:n};return console.log("Returning error response:",e.errorMessage),s.status(r.status).json(e)}}catch(e){return console.error("Error processing liveness check:",e),s.status(500).json({wasProcessed:!1,error:!0,errorMessage:"Failed to process liveness check"})}}e.exports=s,e.exports.default=s}},s={},function o(t){var r=s[t];if(void 0!==r)return r.exports;var n=s[t]={exports:{}};return e[t](n,n.exports,o),n.exports}(154);var e,s}));