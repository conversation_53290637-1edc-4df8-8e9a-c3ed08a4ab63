!function(e,o){"object"==typeof exports&&"object"==typeof module?module.exports=o():"function"==typeof define&&define.amd?define("api",[],o):"object"==typeof exports?exports.api=o():e.api=o()}(this,(()=>{return e={22:e=>{function o(e,o){o.status(200).json({status:"UP"})}e.exports=o,e.exports.default=o},154:(e,o,s)=>{const t=s(198),n=s(264),r=s(235),c=s(622),a=s(22);e.exports={sessionTokenHandler:t,facetecSessionTokenHandler:n,idscanOnlyHandler:r,livenessCheckHandler:c,healthHandler:a}},198:(e,o,s)=>{const t=s(599);async function n(e,o){if("GET"!==e.method)return o.status(405).json({error:"Method not allowed"});try{const s=`${process.env.API_BASE_URL||"https://ekyc-ekyc-dev.np.scbtechx.io"}/v1/ekyc/authen/sessiontoken`,n={...e.headers};if(delete n.host,delete n.connection,n["Content-Type"]="application/json",n.Accept="application/json",!n.Authorization&&process.env.JWT_TOKEN&&(n.Authorization=`Bearer ${process.env.JWT_TOKEN}`),!n["X-Ekyc-Device-Info"]){const e=t.getUniqueId();n["X-Ekyc-Device-Info"]=`browser|${e}`}const r=await fetch(s,{method:"GET",headers:n}),c=await r.json();return o.status(r.status).json(c)}catch(e){return console.error("Error getting session token:",e),o.status(500).json({error:"Failed to get session token"})}}e.exports=n,e.exports.default=n},235:e=>{async function o(e,o){if("POST"!==e.method)return o.status(405).json({error:"Method not allowed"});try{const s=`${process.env.API_BASE_URL||"https://ekyc-ekyc-dev.np.scbtechx.io"}/v1/ekyc/idscan-only`,t={"Content-Type":"application/json",Accept:"application/json",Authorization:e.headers.authorization||(process.env.JWT_TOKEN?`Bearer ${process.env.JWT_TOKEN}`:void 0),"X-Device-Key":e.headers["x-device-key"],"X-User-Agent":e.headers["x-user-agent"],"X-Session-Id":e.headers["x-session-id"],"X-Tid":e.headers["x-tid"],"X-Ekyc-Token":e.headers["x-ekyc-token"],correlationid:e.headers.correlationid};Object.keys(t).forEach((e=>{void 0===t[e]&&delete t[e]}));const n=await fetch(s,{method:"POST",headers:t,body:JSON.stringify(e.body)}),r=await n.json();if(n.ok&&"CUS-KYC-1000"===r.code){const e={wasProcessed:!0,error:!1,scanResultBlob:r.data?.scanResultBlob||"",originalResponse:r};return console.log("Returning success response with scanResultBlob length:",e.scanResultBlob.length),o.status(200).json(e)}{const e={wasProcessed:!1,error:!0,errorMessage:r.description||r.message||"Unknown error",originalResponse:r};return console.log("Returning error response:",e.errorMessage),o.status(n.status).json(e)}}catch(e){return console.error("Error processing ID scan:",e),o.status(500).json({wasProcessed:!1,error:!0,errorMessage:"Failed to process ID scan"})}}e.exports=o,e.exports.default=o},264:(e,o,s)=>{const t=s(599);async function n(e,o){if("GET"!==e.method)return o.status(405).json({error:"Method not allowed"});try{const s=`${process.env.API_BASE_URL||"https://ekyc-ekyc-dev.np.scbtechx.io"}/v1/ekyc/authen/sessiontoken/facetec`,n={...e.headers};if(delete n.host,delete n.connection,n["Content-Type"]="application/json",n.Accept="application/json",!n.Authorization&&process.env.JWT_TOKEN&&(n.Authorization=`Bearer ${process.env.JWT_TOKEN}`),!n["X-Ekyc-Device-Info"]){const e=t.getUniqueId();n["X-Ekyc-Device-Info"]=`browser|${e}`}const r=await fetch(s,{method:"GET",headers:n}),c=await r.json();return o.status(r.status).json(c)}catch(e){return console.error("Error getting FaceTec session token:",e),o.status(500).json({error:"Failed to get FaceTec session token"})}}e.exports=n,e.exports.default=n},599:e=>{e.exports=class{static generateUuid(){return"xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,(function(e){const o=16*Math.random()|0;return("x"===e?o:3&o|8).toString(16)}))}static getDeviceId(){if("undefined"!=typeof window&&window.localStorage){let e=localStorage.getItem("ekyc_device_id");return e||(e=this.generateUuid(),localStorage.setItem("ekyc_device_id",e)),e}return this.generateUuid()}static getUniqueId(){return this.generateUuid()}}},622:e=>{async function o(e,o){if("POST"!==e.method)return o.status(405).json({error:"Method not allowed"});try{const s=(e,o=20)=>{if(!e||"object"!=typeof e)return e;const t={};for(const[n,r]of Object.entries(e))"string"==typeof r&&r.length>o?t[n]=r.substring(0,o)+"...":t[n]="object"==typeof r&&null!==r?s(r,o):r;return t};console.log("=== ENROLLMENT-3D API HANDLER - INCOMING REQUEST ==="),console.log("Request method:",e.method),console.log("Request headers received (truncated):",JSON.stringify(s(e.headers),null,2)),console.log("Request body received (truncated):",JSON.stringify(s(e.body),null,2)),console.log("Request body keys:",Object.keys(e.body||{}));const t=["function","faceScan","auditTrailImage","lowQualityAuditTrailImage"].filter((o=>!e.body||!e.body[o]));t.length>0?console.log("⚠️  MISSING REQUIRED BODY PARAMETERS:",t):console.log("✅ All required body parameters present");const n=`${process.env.API_BASE_URL||"https://ekyc-ekyc-dev.np.scbtechx.io"}/v1/ekyc/enrollment-3d`,r={"Content-Type":"application/json",Accept:"application/json",Authorization:e.headers.authorization||(process.env.JWT_TOKEN?`Bearer ${process.env.JWT_TOKEN}`:void 0),"X-Device-Key":e.headers["x-device-key"],"X-User-Agent":e.headers["x-user-agent"],"X-Session-Id":e.headers["x-session-id"],"X-Tid":e.headers["x-tid"],"X-Ekyc-Token":e.headers["x-ekyc-token"],"X-Ekyc-Sdk-Version":"1.0.0",correlationid:e.headers.correlationid};Object.keys(r).forEach((e=>{void 0===r[e]&&delete r[e]})),console.log("=== ENROLLMENT-3D API HANDLER - OUTGOING REQUEST ==="),console.log("Target URL:",n),console.log("Outgoing headers (truncated):",JSON.stringify(s(r),null,2)),console.log("Outgoing body (truncated):",JSON.stringify(s(e.body),null,2)),console.log("Outgoing body size (bytes):",JSON.stringify(e.body).length);const c=await fetch(n,{method:"POST",headers:r,body:JSON.stringify(e.body)}),a=await c.json();if(console.log("=== ENROLLMENT-3D API HANDLER - BACKEND RESPONSE ==="),console.log("Response status:",c.status),console.log("Response ok:",c.ok),console.log("Response data:",JSON.stringify(a,null,2)),c.ok&&"CUS-KYC-1000"===a.code){const e={wasProcessed:!0,error:!1,scanResultBlob:a.data?.scanResultBlob||"",originalResponse:a};return console.log("✅ SUCCESS - Returning success response with scanResultBlob length:",e.scanResultBlob.length),console.log("=== ENROLLMENT-3D API HANDLER - FINAL RESPONSE ==="),console.log("Final response:",JSON.stringify(e,null,2)),o.status(200).json(e)}{const e={wasProcessed:!1,error:!0,errorMessage:a.description||a.message||"Unknown error",originalResponse:a};return console.log("❌ ERROR - Returning error response:",e.errorMessage),console.log("=== ENROLLMENT-3D API HANDLER - FINAL RESPONSE ==="),console.log("Final response:",JSON.stringify(e,null,2)),o.status(c.status).json(e)}}catch(e){return console.error("Error processing liveness check:",e),o.status(500).json({wasProcessed:!1,error:!0,errorMessage:"Failed to process liveness check"})}}e.exports=o,e.exports.default=o}},o={},function s(t){var n=o[t];if(void 0!==n)return n.exports;var r=o[t]={exports:{}};return e[t](r,r.exports,s),r.exports}(154);var e,o}));